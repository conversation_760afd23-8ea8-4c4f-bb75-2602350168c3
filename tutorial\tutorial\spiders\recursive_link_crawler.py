import scrapy
import json
import os
from datetime import datetime
from urllib.parse import urljoin, urlparse
from tutorial.items import LinkItem


class RecursiveLinkCrawlerSpider(scrapy.Spider):
    name = "recursive_link_crawler"
    
    # Custom settings for this spider - optimized for speed
    custom_settings = {
        'FEED_FORMAT': 'json',
        'FEED_URI': f'link_crawler_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
        'DOWNLOAD_DELAY': 0.25,  # Faster crawling - 0.25 second delay
        'RANDOMIZE_DOWNLOAD_DELAY': False,  # Consistent timing
        'CONCURRENT_REQUESTS': 16,  # More concurrent requests for speed
        'CONCURRENT_REQUESTS_PER_DOMAIN': 8,  # More requests per domain
        'DEPTH_LIMIT': 2,  # Keep depth at 2 for manageable results
        'ROBOTSTXT_OBEY': True,
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/134.0.0.0 Safari/537.3',
        'DUPEFILTER_DEBUG': False,  # Disable debug for speed
        'DOWNLOAD_TIMEOUT': 10,  # 10 second timeout
        'RETRY_TIMES': 1,  # Reduce retries for speed
        'REDIRECT_MAX_TIMES': 3,  # Limit redirects
        'COOKIES_ENABLED': False,  # Disable cookies for speed
        'TELNETCONSOLE_ENABLED': False,  # Disable telnet for speed
    }
    
    def __init__(self, sources_file=None, *args, **kwargs):
        super(RecursiveLinkCrawlerSpider, self).__init__(*args, **kwargs)

        # Allow specifying a different sources file for testing
        if sources_file:
            sources_path = os.path.join(os.path.dirname(__file__), '..', '..', sources_file)
        else:
            sources_path = os.path.join(os.path.dirname(__file__), '..', '..', 'sources.json')

        with open(sources_path, 'r') as f:
            self.sources = json.load(f)
        
        # Create start_urls and allowed_domains from sources.json
        self.start_urls = []
        self.allowed_domains = []
        self.brand_mapping = {}  # Map domain to brand info
        
        for source in self.sources:
            brand = source['brand']
            url = source['url']
            
            # Parse the URL to get domain
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # Remove 'www.' prefix for domain matching
            clean_domain = domain.replace('www.', '') if domain.startswith('www.') else domain
            
            self.start_urls.append(url)
            self.allowed_domains.append(domain)
            if clean_domain != domain:
                self.allowed_domains.append(clean_domain)
            
            # Store brand mapping for this domain
            self.brand_mapping[domain] = {
                'brand': brand,
                'source_url': url
            }
            if clean_domain != domain:
                self.brand_mapping[clean_domain] = {
                    'brand': brand,
                    'source_url': url
                }
        
        # Remove duplicates from allowed_domains
        self.allowed_domains = list(set(self.allowed_domains))
        
        self.logger.info(f"Loaded {len(self.sources)} sources from sources.json")
        self.logger.info(f"Will crawl {len(self.start_urls)} URLs")

    def parse(self, response):
        """Parse the initial page and extract links."""
        # Get brand info for this domain
        domain = urlparse(response.url).netloc
        clean_domain = domain.replace('www.', '') if domain.startswith('www.') else domain
        
        brand_info = self.brand_mapping.get(domain) or self.brand_mapping.get(clean_domain)
        if not brand_info:
            self.logger.warning(f"No brand info found for domain: {domain}")
            return
        
        # Get page title
        page_title = response.css('title::text').get()
        if page_title:
            page_title = page_title.strip()
        
        # Get current depth
        current_depth = response.meta.get('depth', 0)
        
        # Extract all links from the page
        links = response.css('a[href]')
        
        for link in links:
            href = link.css('::attr(href)').get()
            link_text = link.css('::text').get()
            
            if not href:
                continue
                
            # Clean up link text
            if link_text:
                link_text = link_text.strip()
            
            # Convert relative URLs to absolute
            absolute_url = urljoin(response.url, href)
            
            # Parse the absolute URL
            parsed_link = urlparse(absolute_url)
            
            # Skip non-HTTP(S) links
            if parsed_link.scheme not in ['http', 'https']:
                continue
            
            # Check if this link belongs to the same domain
            link_domain = parsed_link.netloc
            clean_link_domain = link_domain.replace('www.', '') if link_domain.startswith('www.') else link_domain
            
            # Only process links from the same domain as the source
            source_domain = urlparse(brand_info['source_url']).netloc
            clean_source_domain = source_domain.replace('www.', '') if source_domain.startswith('www.') else source_domain
            
            if clean_link_domain == clean_source_domain or link_domain == source_domain:
                # Yield the discovered link
                yield LinkItem(
                    brand=brand_info['brand'],
                    source_url=brand_info['source_url'],
                    discovered_url=absolute_url,
                    page_title=page_title,
                    link_text=link_text,
                    depth=current_depth,
                    parent_url=response.url
                )
                
                # Follow the link for recursive crawling (if within depth limit)
                if current_depth < self.settings.get('DEPTH_LIMIT', 3):
                    yield response.follow(
                        absolute_url,
                        callback=self.parse,
                        meta={'depth': current_depth + 1},
                        dont_filter=False  # Allow revisiting URLs at different depths
                    )
