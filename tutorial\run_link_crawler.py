#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the recursive link crawler and organize results by brand.

This script:
1. Runs the recursive_link_crawler spider
2. Processes the raw results
3. Organizes links by brand and source URL
4. Saves the organized results to a JSON file
"""

import json
import os
import subprocess
import sys
from collections import defaultdict


def run_crawler():
    """Run the recursive link crawler spider."""
    print("Starting recursive link crawler...")
    
    # Change to tutorial directory
    tutorial_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(tutorial_dir)
    
    # Run the spider
    try:
        result = subprocess.run([
            sys.executable, '-m', 'scrapy', 'crawl', 'recursive_link_crawler'
        ], capture_output=True, text=True, check=True)
        
        print("Crawler completed successfully!")
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
    except subprocess.CalledProcessError as e:
        print(f"Error running crawler: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    
    return True


def organize_results():
    """Organize the crawler results by brand and source URL."""
    print("Organizing results...")
    
    # Read the raw results
    results_file = 'link_crawler_results.json'
    if not os.path.exists(results_file):
        print(f"Results file {results_file} not found!")
        return False
    
    with open(results_file, 'r') as f:
        raw_results = json.load(f)
    
    print(f"Processing {len(raw_results)} discovered links...")
    
    # Organize by brand
    organized_results = defaultdict(lambda: {
        'brand': '',
        'source_url': '',
        'total_links': 0,
        'unique_links': set(),
        'links_by_depth': defaultdict(list),
        'all_links': []
    })
    
    for item in raw_results:
        brand = item['brand']
        source_url = item['source_url']
        discovered_url = item['discovered_url']
        depth = item['depth']
        
        # Use brand as key
        key = brand
        
        # Initialize brand info
        organized_results[key]['brand'] = brand
        organized_results[key]['source_url'] = source_url
        
        # Add to unique links set
        organized_results[key]['unique_links'].add(discovered_url)
        
        # Add to depth-organized links
        organized_results[key]['links_by_depth'][depth].append({
            'url': discovered_url,
            'page_title': item.get('page_title', ''),
            'link_text': item.get('link_text', ''),
            'parent_url': item.get('parent_url', '')
        })
        
        # Add to all links
        organized_results[key]['all_links'].append({
            'url': discovered_url,
            'page_title': item.get('page_title', ''),
            'link_text': item.get('link_text', ''),
            'parent_url': item.get('parent_url', ''),
            'depth': depth
        })
    
    # Convert to final format
    final_results = {}
    for brand, data in organized_results.items():
        # Convert set to list and sort
        unique_links = sorted(list(data['unique_links']))
        
        # Convert defaultdict to regular dict and sort by depth
        links_by_depth = {}
        for depth in sorted(data['links_by_depth'].keys()):
            links_by_depth[depth] = data['links_by_depth'][depth]
        
        final_results[brand] = {
            'brand': data['brand'],
            'source_url': data['source_url'],
            'total_unique_links': len(unique_links),
            'total_discovered_links': len(data['all_links']),
            'unique_links': unique_links,
            'links_by_depth': links_by_depth,
            'all_discovered_links': data['all_links']
        }
    
    # Save organized results
    organized_file = 'link_crawler_results_organized.json'
    with open(organized_file, 'w') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False)
    
    print(f"Organized results saved to {organized_file}")
    
    # Print summary
    print("\n=== CRAWLING SUMMARY ===")
    for brand, data in final_results.items():
        print(f"{brand}:")
        print(f"  Source URL: {data['source_url']}")
        print(f"  Unique links found: {data['total_unique_links']}")
        print(f"  Total discoveries: {data['total_discovered_links']}")
        print(f"  Max depth reached: {max(data['links_by_depth'].keys()) if data['links_by_depth'] else 0}")
        print()
    
    return True


def main():
    """Main function to run the crawler and organize results."""
    print("=== Recursive Link Crawler ===")
    print("This will crawl all URLs from sources.json and discover internal links.")
    print()
    
    # Run the crawler
    if not run_crawler():
        print("Failed to run crawler. Exiting.")
        return 1
    
    # Organize the results
    if not organize_results():
        print("Failed to organize results. Exiting.")
        return 1
    
    print("=== Crawling Complete ===")
    print("Check the following files for results:")
    print("- link_crawler_results.json (raw results)")
    print("- link_crawler_results_organized.json (organized by brand)")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
